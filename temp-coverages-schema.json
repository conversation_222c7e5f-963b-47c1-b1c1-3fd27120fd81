{"$id": "Coverages", "type": "object", "additionalProperties": false, "required": ["_id", "name", "covered", "type"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "vectorIds": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadIds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}}}}}, "carrierName": {"type": "string"}, "webpage": {"type": "string"}, "name": {"type": "string"}, "openNetwork": {"type": "boolean"}, "plan_type": {"type": "string"}, "type": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"]}, "description": {"type": "string"}, "hsaQualified": {"type": "boolean"}, "productDetailRef": {"type": "string"}, "fortyPremium": {"type": "number"}, "maxAge": {"type": "number"}, "preventive": {"type": "boolean"}, "covered": {"type": "string", "enum": ["individual", "group"]}, "carrierLogo": {"$comment": "***imageSchema used here***", "type": "object"}, "documents": {"$comment": "***imageSchema used here***", "type": "object"}, "postTax": {"type": "boolean"}, "video": {"$comment": "***imageSchema used here***", "type": "object"}, "geo": {"$comment": "***geoJsonSchema used here***", "type": "object"}, "issuer": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "lastSync": {"type": "string"}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "template": {"type": "boolean"}, "fromTemplate": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "public": {"type": "boolean"}, "sim": {"type": "boolean"}, "group_sim_only": {"type": "boolean"}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "listBillDiscount": {"type": "number"}, "dpc": {"type": "boolean"}, "ichra": {"type": "boolean"}, "shop": {"type": "boolean"}, "rates": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}