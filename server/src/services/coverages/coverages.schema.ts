// TypeBox schema for coverages service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, imageSchema, geoJsonSchema } from '../../utils/common/typebox-schemas.js'



export const coveragesSchema = Type.Object({
  _id: ObjectIdSchema(),
  vectorIds: Type.Optional(
    Type.Record(Type.String(), Type.Object({
      uploadIds: Type.Optional(Type.Array(ObjectIdSchema())),
      id: Type.Optional(Type.String()),
      fileIds: Type.Optional(Type.Array(Type.String())),
      updatedAt: Type.Optional(Type.Any())
    }))
  ),
  carrierName: Type.Optional(Type.String()),
  webpage: Type.Optional(Type.String()),
  name: Type.String(),
  openNetwork: Type.Optional(Type.Boolean()),
  plan_type: Type.Optional(Type.String()),
  type: Type.Union([
    Type.Literal("mm"),
    Type.Literal("mec"),
    Type.Literal("hs"),
    Type.Literal("dc"),
    Type.Literal("eb"),
    Type.Literal("hra"),
  ]),
  description: Type.Optional(Type.String()),
  hsaQualified: Type.Optional(Type.Boolean()),
  productDetailRef: Type.Optional(Type.String()),
  fortyPremium: Type.Optional(Type.Number()),
  maxAge: Type.Optional(Type.Number()),
  preventive: Type.Optional(Type.Boolean()),
  covered: Type.Union([Type.Literal("individual"), Type.Literal("group")]),
  carrierLogo: Type.Optional(imageSchema),
  documents: Type.Optional(imageSchema),
  postTax: Type.Optional(Type.Boolean()),
  video: Type.Optional(imageSchema),
  geo: Type.Optional(geoJsonSchema),
  issuer: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  lastSync: Type.Optional(Type.String()),
  provider: Type.Optional(ObjectIdSchema()),
  template: Type.Optional(Type.Boolean()),
  fromTemplate: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
  sim: Type.Optional(Type.Boolean()),
  group_sim_only: Type.Optional(Type.Boolean()),
  contract: Type.Optional(ObjectIdSchema()),
  listBillDiscount: Type.Optional(Type.Number()),
  dpc: Type.Optional(Type.Boolean()),
  ichra: Type.Optional(Type.Boolean()),
  shop: Type.Optional(Type.Boolean()),
  rates: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: "Coverages", additionalProperties: false })

export type Coverages = Static<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)
export const coveragesResolver = resolve<Coverages, HookContext>({})
export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = Type.Object({
  ...Type.Omit(coveragesSchema, ['_id']).properties
}, { additionalProperties: false })

export type CoveragesData = Static<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const coveragesQueryProperties = Type.Pick(coveragesSchema, ['_id', 'issuer', 'org', 'provider', 'fromTemplate', 'contract', 'rates', 'createdBy', 'updatedBy'])

export const coveragesPatchSchema = commonPatch(coveragesSchema, { pushPullOpts: [], pickedForSet: coveragesQueryProperties })
export type CoveragesPatch = Static<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})

export const coveragesQuerySchema = queryWrapper(Type.Object({
  ...coveragesQueryProperties.properties
}, { additionalProperties: true }))
export type CoveragesQuery = Static<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})

// Export for backward compatibility
export const coverCopySchema = coveragesSchema
