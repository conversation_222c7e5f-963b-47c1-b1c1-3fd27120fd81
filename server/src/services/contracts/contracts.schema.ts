// TypeBox schema for contracts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, updatesSchema } from '../../utils/common/typebox-schemas.js'
import { sectionsSchema } from '../plan-docs/utils/schemas.js'
const paySchema = Type.Optional(
    Type.Object({
      fee: Type.Optional(Type.Number()),
      feeType: Type.Optional(
          Type.Union([
            Type.Literal("alg"),
            Type.Literal("pepm"),
            Type.Literal("pmpm"),
            Type.Literal("flat"),
          ])
      ),
      feeDescription: Type.Optional(Type.String()),
      hostSplitType: Type.Optional(
          Type.Union([
            Type.Literal("percent"),
            Type.Literal("flat"),
            Type.Literal("pepm"),
            Type.Literal("pmpm"),
          ])
      ),
      hostSplitAmount: Type.Optional(Type.Number()),
      refSplit: Type.Optional(
          Type.Record(Type.String(), Type.Object({
            percent: Type.Optional(Type.Number()),
            ref: Type.Optional(ObjectIdSchema())
          }))
      )
    })
)
export const contractsSchema = Type.Object({
  _id: ObjectIdSchema(),
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  status: Type.Optional(
    Type.Union([
      Type.Literal("open"),
      Type.Literal("sent"),
      Type.Literal("rejected"),
      Type.Literal("executed"),
    ])
  ),
  rejectedBy: Type.Optional(updatesSchema),
  pTags: Type.Optional(Type.Array(Type.String())),
  parties: Type.Optional(
    Type.Record(Type.String(), Type.Object({
      relationship: Type.Optional(Type.String()),
      by: Type.Optional(Type.String()),
      byTitle: Type.Optional(Type.String()),
      id: Type.Optional(ObjectIdSchema()),
      idService: Type.Optional(Type.String()),
      aka: Type.Optional(Type.String()),
      tag: Type.Optional(Type.String()),
      legalName: Type.Optional(Type.String()),
      address: Type.Optional(Type.String()),
      email: Type.Optional(Type.String()),
      phone: Type.Optional(Type.String()),
      ack: Type.Optional(Type.Object({
        acceptedAt: Type.Optional(Type.Any()),
        ip: Type.Optional(Type.String()),
        ua: Type.Optional(Type.String()),
        copy: Type.Optional(Type.String()),
        fingerprint: Type.Optional(ObjectIdSchema()),
        login: Type.Optional(ObjectIdSchema()),
        email: Type.Optional(Type.String()),
        phone: Type.Optional(Type.String()),
        signature: Type.Optional(Type.String())
      }))
    }))
  ),
  meta: Type.Optional(
    Type.Object({
      pay: paySchema
    }, { additionalProperties: true })
  ),
  sections: sectionsSchema,
  ...commonFields
}, { $id: "Contracts", additionalProperties: false })

export type Contracts = Static<typeof contractsSchema>
export const contractsValidator = getValidator(contractsSchema, dataValidator)
export const contractsResolver = resolve<Contracts, HookContext>({})
export const contractsExternalResolver = resolve<Contracts, HookContext>({})

export const contractsDataSchema = Type.Object({
  ...Type.Omit(contractsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ContractsData = Static<typeof contractsDataSchema>
export const contractsDataValidator = getValidator(contractsDataSchema, dataValidator)
export const contractsDataResolver = resolve<ContractsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const contractsQueryProperties = Type.Pick(contractsSchema, ['_id', 'subject', 'owner', 'managers', 'createdBy', 'updatedBy'])

export const contractsPatchSchema = commonPatch(contractsSchema, { pushPullOpts: [], pickedForSet: contractsQueryProperties })
export type ContractsPatch = Static<typeof contractsPatchSchema>
export const contractsPatchValidator = getValidator(contractsPatchSchema, dataValidator)
export const contractsPatchResolver = resolve<ContractsPatch, HookContext>({})
export const contractsQuerySchema = queryWrapper(Type.Object({
  ...contractsQueryProperties.properties
}, { additionalProperties: true }))
export type ContractsQuery = Static<typeof contractsQuerySchema>
export const contractsQueryValidator = getValidator(contractsQuerySchema, queryValidator)
export const contractsQueryResolver = resolve<ContractsQuery, HookContext>({})
